"use client"

import * as React from "react"
import * as TabsPrimitive from "@radix-ui/react-tabs"
import { cn } from "@/lib/utils"

// Variant styles for different tab appearances
const getTabVariants = (expanded = false) => ({
  folder: {
    container: "flex flex-col gap-0",
    list: `inline-flex h-auto ${expanded ? 'w-full' : 'w-fit'} items-end justify-start bg-transparent p-0 gap-0`,
    trigger: `
      relative inline-flex items-center justify-center px-4 py-2 text-sm font-medium
      ${expanded ? 'flex-1' : ''}
      border-b-2 border-transparent bg-gray-100 text-gray-600
      hover:bg-gray-200 hover:text-gray-800
      data-[state=active]:bg-white data-[state=active]:text-gray-900
      data-[state=active]:border-blue-500 data-[state=active]:border-b-2
      focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500
      focus-visible:data-[state=active]:ring-transparent
      disabled:pointer-events-none disabled:opacity-50
      transition-all duration-200
      first:rounded-tl-md last:rounded-tr-md
      border-l border-r border-t border-gray-300
      data-[state=active]:border-l-blue-500 data-[state=active]:border-r-blue-500
      data-[state=active]:border-t-blue-500 data-[state=active]:z-10
      data-[state=active]:border-b-transparent
      -mb-px
    `,
    content: (expanded) => expanded
      ? "flex-1 outline-none bg-white border border-gray-300 border-t-0 rounded-b-md p-4"
      : "flex-1 outline-none bg-white border border-gray-300 rounded-b-md p-4",
    contentExpanded: "flex-1 outline-none bg-white border border-gray-300 border-t-0 rounded-b-md p-4",
    contentNotExpanded: "flex-1 outline-none bg-white border border-gray-300 rounded-b-md p-4"
  },
  pills: {
    container: "flex flex-col gap-2",
    list: (expanded) => `bg-muted text-muted-foreground inline-flex h-9 ${expanded ? 'w-full' : 'w-fit'} items-center justify-center rounded-lg p-[3px]`,
    trigger: `
      data-[state=active]:bg-background dark:data-[state=active]:text-foreground
      focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring
      dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30
      text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)]
      flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent
      px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow]
      focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none
      disabled:opacity-50 data-[state=active]:shadow-sm
    `,
    content: "flex-1 outline-none"
  }
})

/**
 * Pf - Main Tab Container Component
 * A controlled component that functions like a RadioGroup
 * Supports both numeric (integer) and string values
 *
 * @param {Object} props
 * @param {string|number} props.value - Current active tab value
 * @param {function} props.onValueChange - Callback when tab changes
 * @param {string} props.variant - Visual variant ('folder' | 'pills')
 * @param {boolean} props.expanded - Whether triggers should expand to fill available space
 * @param {boolean} props.skipContentFocus - If true, TabsContent won't be focusable (reduces tab navigation steps)
 * @param {string} props.className - Additional CSS classes
 * @param {React.ReactNode} props.children - Tab content components (Pg components)
 */
function Pf({
  value,
  onValueChange,
  variant = "folder",
  expanded = false,
  skipContentFocus = false,
  className,
  children,
  ...props
}) {
  // Convert value to string for Radix UI compatibility
  const stringValue = value !== undefined ? String(value) : undefined
  
  // Handle value change and convert back to original type if needed
  const handleValueChange = React.useCallback((newValue) => {
    if (!onValueChange) return
    
    // Try to determine if original value was numeric
    const originalValue = value
    if (typeof originalValue === 'number') {
      const numericValue = Number(newValue)
      if (!isNaN(numericValue)) {
        onValueChange(numericValue)
        return
      }
    }
    
    // Return as string
    onValueChange(newValue)
  }, [value, onValueChange])

  // Get variant styles
  const variantStyles = getTabVariants(expanded)[variant] || getTabVariants(expanded).folder

  // Extract tab triggers and content from children
  const tabTriggers = []
  const tabContents = []

  React.Children.forEach(children, (child) => {
    if (React.isValidElement(child) && child.type.displayName === 'Pg') {
      const childValue = String(child.props.value)
      const isDisabled = child.props.disabled || false

      // Create trigger button
      tabTriggers.push(
        <TabsPrimitive.Trigger
          key={childValue}
          value={childValue}
          disabled={isDisabled}
          className={cn(variantStyles.trigger)}
        >
          {child.props.label || `Tab ${child.props.value}`}
        </TabsPrimitive.Trigger>
      )

      // Store content for rendering
      tabContents.push(child)
    }
  })

  return (
    <TabsPrimitive.Root
      value={stringValue}
      onValueChange={handleValueChange}
      className={cn(variantStyles.container, className)}
      {...props}
    >
      <TabsPrimitive.List className={cn(
        typeof variantStyles.list === 'function'
          ? variantStyles.list(expanded)
          : variantStyles.list
      )}>
        {tabTriggers}
      </TabsPrimitive.List>

      {tabContents.map((content) => {
        const contentClassName = variant === 'folder'
          ? (expanded
              ? variantStyles.contentExpanded
              : variantStyles.contentNotExpanded)
          : variantStyles.content

        return (
          <TabsPrimitive.Content
            key={String(content.props.value)}
            value={String(content.props.value)}
            tabIndex={skipContentFocus ? -1 : 0}
            className={cn(contentClassName)}
          >
            {content.props.children}
          </TabsPrimitive.Content>
        )
      })}
    </TabsPrimitive.Root>
  )
}

Pf.displayName = "Pf"

export { Pf }
