"use client"

import { useState } from "react"
import { Pf } from "@/components/ui/Pf"
import { Pg } from "@/components/ui/Pg"
import { Input } from "@/components/ui/input"

export default function Home() {
  // State for string values example
  const [pgActivaString, setPgActivaString] = useState("perfil")

  // State for numeric values example
  const [pgActivaNumeric, setPgActivaNumeric] = useState(1)

  // State for pills variant example
  const [pgActivaPills, setPgActivaPills] = useState("settings")

  // State for expanded tabs example
  const [pgActivaExpanded, setPgActivaExpanded] = useState("tab1")

  // State for disabled tabs example
  const [pgActivaDisabled, setPgActivaDisabled] = useState("available1")

  // State for skip content focus example
  const [pgActivaSkipFocus, setPgActivaSkipFocus] = useState("tab1")

  return (
    <div className="min-h-screen p-8 pb-20 gap-16 sm:p-20 font-[family-name:var(--font-geist-sans)]">
      <main className="max-w-4xl mx-auto space-y-12">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Componentes de Pestañas Personalizados
          </h1>
          <p className="text-gray-600">
            Ejemplos de uso de los componentes Pf y Pg con valores string y numéricos
          </p>
        </div>

        {/* Example 1: String values with folder variant (default) */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-800">
            Ejemplo 1: Valores String (Estilo Carpetas)
          </h2>
          <p className="text-sm text-gray-600 mb-4">
            Valor actual: <code className="bg-gray-100 px-2 py-1 rounded">{pgActivaString}</code>
          </p>
          <p className="text-xs text-blue-600 mb-4">
            💡 <strong>Navegación por teclado:</strong> Desde el input anterior, necesitas <strong>3 pulsaciones de Tab</strong>
            para llegar al input interno (comportamiento estándar ARIA).
          </p>

          <Input type="text" placeholder="Input anterior al componente Pf" className="mb-4" />

          <Pf value={pgActivaString} onValueChange={setPgActivaString}>
            <Pg value="perfil" label="Perfil">
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Contenido de tab [perfil]</h3>
                <Input type="text" placeholder="Input interno del tab perfil (3 Tabs desde el anterior)" />
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-blue-800">
                    Esta es la sección de perfil del usuario. Aquí puedes ver y editar
                    tu información personal.
                  </p>
                  <div className="mt-4 space-y-2">
                    <div className="flex justify-between">
                      <span className="font-medium">Nombre:</span>
                      <span>Juan Pérez</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Email:</span>
                      <span><EMAIL></span>
                    </div>
                  </div>
                </div>
              </div>
            </Pg>
            <Pg value="detalle" label="Detalles">
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Contenido de tab [detalle]</h3>
                <div className="bg-green-50 p-4 rounded-lg">
                  <p className="text-green-800">
                    Aquí se muestran los detalles adicionales y configuraciones avanzadas.
                  </p>
                  <ul className="mt-4 space-y-1 text-green-700">
                    <li>• Configuración de privacidad</li>
                    <li>• Preferencias de notificación</li>
                    <li>• Historial de actividad</li>
                  </ul>
                </div>
              </div>
            </Pg>
            <Pg value="configuracion" label="Configuración">
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Contenido de tab [configuracion]</h3>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <p className="text-purple-800">
                    Panel de configuración del sistema y preferencias del usuario.
                  </p>
                  <div className="mt-4 grid grid-cols-2 gap-4">
                    <div className="bg-white p-3 rounded border">
                      <h4 className="font-medium">Tema</h4>
                      <p className="text-sm text-gray-600">Claro/Oscuro</p>
                    </div>
                    <div className="bg-white p-3 rounded border">
                      <h4 className="font-medium">Idioma</h4>
                      <p className="text-sm text-gray-600">Español</p>
                    </div>
                  </div>
                </div>
              </div>
            </Pg>
          </Pf>

          <Input type="text" placeholder="Input posterior al componente Pf" className="mt-4" />
        </section>

        {/* Example 2: Numeric values with folder variant */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-800">
            Ejemplo 2: Valores Numéricos (Estilo Carpetas)
          </h2>
          <p className="text-sm text-gray-600 mb-4">
            Valor actual: <code className="bg-gray-100 px-2 py-1 rounded">{pgActivaNumeric}</code> (tipo: {typeof pgActivaNumeric})
          </p>

          <Pf value={pgActivaNumeric} onValueChange={setPgActivaNumeric}>
            <Pg value={1} label="Paso 1">
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Contenido de tab 1</h3>
                <div className="bg-orange-50 p-4 rounded-lg">
                  <p className="text-orange-800">
                    Primer paso del proceso. Aquí comenzamos con la configuración inicial.
                  </p>
                  <div className="mt-4 p-3 bg-white rounded border-l-4 border-orange-400">
                    <p className="text-sm">
                      <strong>Instrucciones:</strong> Complete los campos requeridos para continuar.
                    </p>
                  </div>
                </div>
              </div>
            </Pg>
            <Pg value={2} label="Paso 2">
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Contenido de tab 2</h3>
                <div className="bg-teal-50 p-4 rounded-lg">
                  <p className="text-teal-800">
                    Segundo paso del proceso. Revisión y validación de datos.
                  </p>
                  <div className="mt-4 flex items-center space-x-2">
                    <div className="w-4 h-4 bg-teal-500 rounded-full"></div>
                    <span className="text-sm text-teal-700">Validación completada</span>
                  </div>
                </div>
              </div>
            </Pg>
            <Pg value={3} label="Paso 3">
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Contenido de tab 3</h3>
                <div className="bg-indigo-50 p-4 rounded-lg">
                  <p className="text-indigo-800">
                    Paso final del proceso. Confirmación y resumen.
                  </p>
                  <div className="mt-4 bg-white p-3 rounded border">
                    <h4 className="font-medium text-indigo-900">Resumen</h4>
                    <p className="text-sm text-indigo-700 mt-1">
                      Todos los pasos han sido completados exitosamente.
                    </p>
                  </div>
                </div>
              </div>
            </Pg>
          </Pf>
        </section>

        {/* Example 3: Pills variant with string values */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-800">
            Ejemplo 3: Variante Pills (Valores String)
          </h2>
          <p className="text-sm text-gray-600 mb-4">
            Valor actual: <code className="bg-gray-100 px-2 py-1 rounded">{pgActivaPills}</code>
          </p>

          <Pf value={pgActivaPills} onValueChange={setPgActivaPills} variant="pills">
            <Pg value="dashboard" label="Dashboard">
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Dashboard</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-blue-100 p-4 rounded-lg">
                    <h4 className="font-semibold text-blue-900">Usuarios</h4>
                    <p className="text-2xl font-bold text-blue-800">1,234</p>
                  </div>
                  <div className="bg-green-100 p-4 rounded-lg">
                    <h4 className="font-semibold text-green-900">Ventas</h4>
                    <p className="text-2xl font-bold text-green-800">$45,678</p>
                  </div>
                  <div className="bg-purple-100 p-4 rounded-lg">
                    <h4 className="font-semibold text-purple-900">Pedidos</h4>
                    <p className="text-2xl font-bold text-purple-800">567</p>
                  </div>
                </div>
              </div>
            </Pg>
            <Pg value="analytics" label="Analytics">
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Analytics</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-700">
                    Gráficos y métricas de rendimiento del sistema.
                  </p>
                  <div className="mt-4 h-32 bg-gradient-to-r from-blue-200 to-purple-200 rounded flex items-center justify-center">
                    <span className="text-gray-600">📊 Gráfico de ejemplo</span>
                  </div>
                </div>
              </div>
            </Pg>
            <Pg value="settings" label="Settings">
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Settings</h3>
                <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                  <div className="flex items-center justify-between">
                    <span>Notificaciones</span>
                    <button className="bg-blue-500 text-white px-3 py-1 rounded text-sm">
                      Activado
                    </button>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Modo oscuro</span>
                    <button className="bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm">
                      Desactivado
                    </button>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Auto-guardado</span>
                    <button className="bg-blue-500 text-white px-3 py-1 rounded text-sm">
                      Activado
                    </button>
                  </div>
                </div>
              </div>
            </Pg>
          </Pf>
        </section>

        {/* Example 4: Expanded tabs */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-800">
            Ejemplo 4: Tabs Expandidos (expanded=true)
          </h2>
          <p className="text-sm text-gray-600 mb-4">
            Los botones de trigger se expanden para llenar el espacio disponible.
            Valor actual: <code className="bg-gray-100 px-2 py-1 rounded">{pgActivaExpanded}</code>
          </p>

          <Pf value={pgActivaExpanded} onValueChange={setPgActivaExpanded} expanded={true}>
            <Pg value="tab1" label="Tab Expandido 1">
              <div className="bg-cyan-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900">Contenido Tab 1</h3>
                <p className="text-cyan-800">
                  Este ejemplo muestra cómo los tabs se expanden para llenar todo el ancho disponible.
                </p>
              </div>
            </Pg>
            <Pg value="tab2" label="Tab Expandido 2">
              <div className="bg-rose-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900">Contenido Tab 2</h3>
                <p className="text-rose-800">
                  Nota cómo el borde superior es visible cuando expanded=true.
                </p>
              </div>
            </Pg>
            <Pg value="tab3" label="Tab Expandido 3">
              <div className="bg-amber-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900">Contenido Tab 3</h3>
                <p className="text-amber-800">
                  Los tres tabs ocupan el mismo ancho proporcionalmente.
                </p>
              </div>
            </Pg>
          </Pf>
        </section>

        {/* Example 5: Disabled tabs */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-800">
            Ejemplo 5: Tabs con Disabled
          </h2>
          <p className="text-sm text-gray-600 mb-4">
            Algunos tabs pueden estar deshabilitados.
            Valor actual: <code className="bg-gray-100 px-2 py-1 rounded">{pgActivaDisabled}</code>
          </p>

          <Pf value={pgActivaDisabled} onValueChange={setPgActivaDisabled}>
            <Pg value="available1" label="Disponible 1">
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900">Tab Disponible 1</h3>
                <p className="text-green-800">
                  Este tab está disponible y se puede seleccionar.
                </p>
              </div>
            </Pg>
            <Pg value="disabled1" label="Deshabilitado" disabled={true}>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900">Tab Deshabilitado</h3>
                <p className="text-gray-600">
                  Este contenido no debería ser visible porque el tab está deshabilitado.
                </p>
              </div>
            </Pg>
            <Pg value="available2" label="Disponible 2">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-900">Tab Disponible 2</h3>
                <p className="text-blue-800">
                  Este tab también está disponible para selección.
                </p>
              </div>
            </Pg>
          </Pf>
        </section>

        {/* Example 6: Skip Content Focus */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-800">
            Ejemplo 6: Navegación Optimizada (skipContentFocus=true)
          </h2>
          <p className="text-sm text-gray-600 mb-4">
            Con <code className="bg-gray-100 px-2 py-1 rounded">skipContentFocus={true}</code>,
            la navegación por teclado es más fluida.
            Valor actual: <code className="bg-gray-100 px-2 py-1 rounded">{pgActivaSkipFocus}</code>
          </p>
          <p className="text-xs text-green-600 mb-4">
            💡 <strong>Navegación optimizada:</strong> Desde el input anterior, solo necesitas <strong>2 pulsaciones de Tab</strong>
            para llegar al input interno (experiencia más fluida).
          </p>

          <Input type="text" placeholder="Input anterior al componente Pf" className="mb-4" />

          <Pf value={pgActivaSkipFocus} onValueChange={setPgActivaSkipFocus} skipContentFocus={true}>
            <Pg value="tab1" label="Tab con Input">
              <div className="bg-emerald-50 p-4 rounded-lg space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Contenido con Input Interno</h3>
                <p className="text-emerald-800">
                  Este tab tiene navegación optimizada. El TabsContent no es enfocable.
                </p>
                <Input type="text" placeholder="Input interno - solo 2 Tabs desde el anterior" />
                <p className="text-sm text-emerald-700">
                  Prueba la navegación: Input anterior → Tab → Trigger → Tab → Input interno
                </p>
              </div>
            </Pg>
            <Pg value="tab2" label="Otro Tab">
              <div className="bg-cyan-50 p-4 rounded-lg space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Segundo Tab</h3>
                <p className="text-cyan-800">
                  Este tab también tiene navegación optimizada.
                </p>
                <Input type="text" placeholder="Otro input interno" />
              </div>
            </Pg>
          </Pf>

          <Input type="text" placeholder="Input posterior al componente Pf" className="mt-4" />
        </section>
      </main>

      <footer className="mt-16 text-center text-gray-500 text-sm">
        Prueba de componentes especiales - Pf y Pg
      </footer>
    </div>
  );
}
