"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

/**
 * Pg - Tab Content Component
 * Based on Shadcn's TabsContent component
 * Works as a child of the Pf component
 *
 * @param {Object} props
 * @param {string|number} props.value - The value that identifies this tab content
 * @param {string} props.label - Optional label for the tab trigger (if not provided, uses "Tab {value}")
 * @param {boolean} props.disabled - Whether this tab should be disabled
 * @param {string} props.className - Additional CSS classes
 * @param {React.ReactNode} props.children - The content to display when this tab is active
 */
function Pg({
  value,
  label,
  disabled = false,
  className,
  children,
  ...props
}) {
  // This component is primarily a data container for the Pf component
  // The actual rendering is handled by the parent Pf component
  // This component serves as a declarative way to define tab content
  
  return (
    <div
      className={cn("pg-content", className)}
      data-value={value}
      data-label={label}
      data-disabled={disabled}
      {...props}
    >
      {children}
    </div>
  )
}

Pg.displayName = "Pg"

export { Pg }
